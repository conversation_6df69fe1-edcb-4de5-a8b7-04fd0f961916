{"name": "rag-project", "version": "1.0.0", "description": "Dockerized TypeScript project with Langchain, Chroma, and RAG capabilities", "main": "dist/app.js", "scripts": {"start": "node dist/app.js", "dev": "nodemon src/app.ts", "build": "tsc", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@langchain/community": "^0.0.50", "@langchain/google-genai": "^0.0.26", "@langchain/openai": "^0.0.28", "@types/cors": "^2.8.19", "@types/express": "^4.17.21", "@types/multer": "^1.4.13", "@types/node": "^20.12.7", "chromadb": "^1.8.1", "chromadb-default-embed": "^2.13.2", "cors": "^2.8.5", "dotenv": "^17.0.0", "express": "^4.19.2", "langchain": "^0.1.32", "mammoth": "^1.7.2", "multer": "^2.0.1", "pdf-parse": "^1.1.1", "typescript": "^5.4.5"}, "devDependencies": {"@types/pdf-parse": "^1.1.5", "nodemon": "^3.1.0"}}