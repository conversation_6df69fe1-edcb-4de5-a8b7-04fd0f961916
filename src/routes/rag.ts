import { Router } from "express";
import { retrieveDocuments } from "../services/langchainService";
import { ChatGoogleGenerativeAI } from "@langchain/google-genai";

const router = Router();

// Helper function to reformulate question with history context

const isArabic = (text: string) => {
    const result = /[\u0600-\u06FF\u0750-\u077F]/.test(text);
    return result;
};
const reformulateQuestion = async (
  question: string,
  history: Array<{ role: string; content: string }>
): Promise<string> => {
  if (!history || history.length === 0) {
    return question;
  }

  const chat = new ChatGoogleGenerativeAI({
    model: "gemini-2.0-flash",

    apiKey: process.env.GOOGLE_API_KEY,
  });

  const historyContext = history
    .map((msg) => `${msg.role}: ${msg.content}`)
    .join("\n");

    const lang = isArabic(question) ? "arabic" : "english";

  const reformulationPrompt = `
 Given a chat history and the latest user question which might reference context in the chat history, 
 formulate a standalone question which can be understood without the chat history. Do NOT answer the question
 reformulate the question and return it in ${lang}
              just reformulate it if needed and otherwise return it as is

Conversation History:
${historyContext}

Current Question: ${question}

Reformulated Question:`;

  try {
    const response = await chat.invoke([
      {
        role: "user",
        content: reformulationPrompt,
      },
    ]);

    return response.content as string;
  } catch (error) {
    console.error("Error reformulating question:", error);
    return question; // Fallback to original question
  }
};

router.post("/rag", async (req, res) => {
  const { question, history = [] } = req.body;

  if (!question) {
    return res.status(400).send("Question is required.");
  }

  try {
    const collectionName = "mwanrag"; // Should match the collection name used in process.ts

    // Reformulate question with history context for better retrieval
    const reformulatedQuestion = await reformulateQuestion(question, history);
    // const reformulatedQuestion = await reformulateQuestion(question, history);
    console.log("Original question:", question);
    console.log("Reformulated question:", reformulatedQuestion);

    const relevantDocs = await retrieveDocuments(
      reformulatedQuestion,
      collectionName,
      500
    );
    // console.log(relevantDocs);

    const context = relevantDocs.map((doc) => doc.pageContent).join("\n\n");

    const chat = new ChatGoogleGenerativeAI({
      model: "gemini-2.0-flash",
      // temperature: 0.7,
      apiKey: process.env.GOOGLE_API_KEY,
    });
    const lang = isArabic(question) ? "arabic" : "english";

    // Build conversation messages including history
    const messages = [
      {
        role: "system",
        content: `
        You are a helpful AI assistant. 
        your persona is that you are an assistant built by infotointell.

        Use the following context to answer the user's question. 
        - if the question is greeting be friendly and return a greeting.
        - Be aware of the conversation history.
        - Provide accurate, helpful responses based on the context provided.
        
        - don't add extra information that is not in the context.
        - if a piece of information is not in the context say "I don't know" and don't try to make up an answer.
        - if the user question is not related to the context say and its not greeting "I don't know" and don't try to make up an answer.
        - your final answer should have no grammatical errors or text formatting errors.
        last user question : ${question}
        always answer in markdown format.

        answer in ${lang}
        Context from documents:
        ${context}`,
      },
    ];

    // Add conversation history
    if (history && history.length > 0) {
      history.forEach((msg: { role: string; content: string }) => {
        messages.push({
          role: msg.role === "user" ? "user" : "assistant",
          content: msg.content,
        });
      });
    }

    // Add current question
    messages.push({
      role: "user",
      content: question,
    });

    const response = await chat.invoke(messages);

    res.status(200).json({
      answer: response.content,
      reformulatedQuestion:
        reformulatedQuestion !== question ? reformulatedQuestion : undefined,
    });
  } catch (error) {
    console.error("Error in RAG endpoint:", error);
    res.status(500).send("Error processing RAG request.");
  }
});

export default router;
