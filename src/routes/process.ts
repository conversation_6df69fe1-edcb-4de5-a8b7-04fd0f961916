import { Router } from "express";
import { extractTextFromFile } from "../utils/fileProcessor";
import {
  splitTextIntoChunks,
  embedAndStoreDocuments,
} from "../services/langchainService";
import { deleteCollection } from "../services/chromaService";
import path from "path";
import fs from "fs";

const router = Router();

router.get("/process", async (req, res) => {
  try {
    const inputFilesDir = path.join(__dirname, "../../input_files");

    // Check if input_files directory exists
    if (!fs.existsSync(inputFilesDir)) {
      return res.status(404).json({
        success: false,
        message: "input_files directory not found.",
      });
    }

    // Read all files from input_files directory
    const files = fs.readdirSync(inputFilesDir);

    if (files.length === 0) {
      return res.status(200).json({
        success: true,
        message: "No files found in input_files directory.",
        processedFiles: [],
      });
    }

    const collectionName = "mwanrag";
    const processedFiles: string[] = [];
    const failedFiles: { filename: string; error: string }[] = [];
    let totalDocuments = 0;

    console.log(`Starting to process ${files.length} files...`);

    // Process each file
    for (const filename of files) {
      const filePath = path.join(inputFilesDir, filename);

      // Skip directories and hidden files
      if (fs.statSync(filePath).isDirectory() || filename.startsWith(".")) {
        continue;
      }

      try {
        console.log(`Processing file: ${filename}`);
        const text = await extractTextFromFile(filePath);
        const docs = await splitTextIntoChunks(text);

        // Add filename as metadata to each document
        const docsWithMetadata = docs.map((doc) => ({
          ...doc,
          metadata: {
            ...doc.metadata,
            filename: filename,
            source: filePath,
          },
        }));

        await embedAndStoreDocuments(docsWithMetadata, collectionName);

        processedFiles.push(filename);
        totalDocuments += docs.length;
        console.log(
          `Successfully processed ${filename} - ${docs.length} chunks`
        );
      } catch (error) {
        console.error(`Error processing file ${filename}:`, error);
        failedFiles.push({
          filename: filename,
          error: error instanceof Error ? error.message : String(error),
        });
      }
    }

    const response = {
      success: true,
      message: `Processing completed. ${processedFiles.length} files processed successfully, ${failedFiles.length} files failed.`,
      processedFiles: processedFiles,
      failedFiles: failedFiles,
      totalDocuments: totalDocuments,
      collectionName: collectionName,
    };

    console.log("Processing summary:", response);
    res.status(200).json(response);
  } catch (error) {
    console.error("Error in process endpoint:", error);
    res.status(500).json({
      success: false,
      message: "Error processing files.",
      error: error instanceof Error ? error.message : String(error),
    });
  }
});

// Clear collection endpoint
router.delete("/clear/:collectionName", async (req, res) => {
  try {
    const { collectionName } = req.params;
    const success = await deleteCollection(collectionName);

    if (success) {
      res.status(200).json({
        success: true,
        message: `Collection ${collectionName} cleared successfully.`,
      });
    } else {
      res.status(500).json({
        success: false,
        message: `Failed to clear collection ${collectionName}.`,
      });
    }
  } catch (error) {
    console.error("Error clearing collection:", error);
    res.status(500).json({
      success: false,
      message: "Error clearing collection.",
      error: error instanceof Error ? error.message : String(error),
    });
  }
});

export default router;
