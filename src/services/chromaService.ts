import { ChromaClient, IncludeEnum } from "chromadb";

const CHROMA_DB_URL = process.env.CHROMA_DB_URL || "http://localhost:8000";

const client = new ChromaClient({ path: CHROMA_DB_URL });

export const getOrCreateCollection = async (collectionName: string) => {
  const collection = await client.getOrCreateCollection({
    name: collectionName,
  });
  return collection;
};

export const addDocumentsToCollection = async (
  collectionName: string,
  documents: string[],
  metadatas: Record<string, any>[],
  ids: string[]
) => {
  const collection = await getOrCreateCollection(collectionName);
  await collection.add({
    documents: documents,
    metadatas: metadatas,
    ids: ids,
  });
  console.log(
    `Added ${documents.length} documents to collection ${collectionName}`
  );
};

export const queryCollection = async (
  collectionName: string,
  queryTexts: string[],
  nResults: number = 2
) => {
  const collection = await getOrCreateCollection(collectionName);
  const results = await collection.query({
    queryTexts: queryTexts,
    nResults: nResults,
  });
  return results;
};

export const queryCollectionWithEmbeddings = async (
  collectionName: string,
  queryEmbeddings: number[],
  nResults: number = 4
) => {
  const collection = await getOrCreateCollection(collectionName);
  const results = await collection.query({
    queryEmbeddings: [queryEmbeddings],
    nResults: nResults,
  });
  return results;
};

export const deleteCollection = async (collectionName: string) => {
  try {
    await client.deleteCollection({ name: collectionName });
    console.log(`Collection ${collectionName} deleted successfully`);
    return true;
  } catch (error) {
    console.error(`Error deleting collection ${collectionName}:`, error);
    return false;
  }
};

export const getAllDocumentsFromCollection = async (collectionName: string) => {
  const collection = await getOrCreateCollection(collectionName);

  // First get the count to determine how many documents we have
  const count = await collection.count();

  if (count === 0) {
    return {
      documents: [],
      metadatas: [],
      ids: [],
      count: 0,
    };
  }

  // Get all documents with their metadata and content
  const results = await collection.get({
    include: [IncludeEnum.Documents, IncludeEnum.Metadatas],
    limit: count, // Get all documents
  });

  return {
    documents: results.documents,
    metadatas: results.metadatas,
    ids: results.ids,
    count: count,
  };
};
