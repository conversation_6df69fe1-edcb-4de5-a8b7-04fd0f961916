import fs from "fs";
import mammoth from "mammoth";
import pdf from "pdf-parse";

export const extractTextFromFile = async (
  filePath: string
): Promise<string> => {
  const fileExtension = filePath.split(".").pop()?.toLowerCase();

  if (!fileExtension) {
    throw new Error(`Unable to determine file extension for: ${filePath}`);
  }

  try {
    switch (fileExtension) {
      case "txt":
        const txtContent = fs.readFileSync(filePath, "utf-8");
        if (!txtContent.trim()) {
          throw new Error("Text file is empty");
        }
        return txtContent;

      case "docx":
        const result = await mammoth.extractRawText({ path: filePath });
        if (!result.value.trim()) {
          throw new Error("DOCX file contains no readable text");
        }
        return result.value;

      case "pdf":
        const dataBuffer = fs.readFileSync(filePath);
        const data = await pdf(dataBuffer);
        if (!data.text.trim()) {
          throw new Error("PDF file contains no readable text");
        }
        return data.text;

      default:
        throw new Error(
          `Unsupported file type: ${fileExtension}. Supported types: txt, docx, pdf`
        );
    }
  } catch (error) {
    if (error instanceof Error) {
      throw new Error(
        `Failed to process ${fileExtension} file: ${error.message}`
      );
    }
    throw new Error(`Failed to process ${fileExtension} file: Unknown error`);
  }
};
