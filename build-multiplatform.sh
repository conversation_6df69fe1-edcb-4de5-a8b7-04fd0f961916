#!/bin/bash

# Create a new builder instance
docker buildx create --name mybuilder --use

# Build backend image for multiple platforms and output to local tar
docker buildx build --platform linux/amd64,linux/arm64 \
  --output "type=docker,dest=rag-backend.tar" \
  -t rag/rag-backend:latest .

# Build frontend image
docker buildx build --platform linux/amd64,linux/arm64 \
  --output "type=docker,dest=rag-frontend.tar" \
  -t rag/rag-frontend:latest ./frontend

# Package everything together
tar -czvf rag-docker-images.tar.gz rag-backend.tar rag-frontend.tar