# Running the RAG Application from Provided Images

## Prerequisites
- <PERSON><PERSON> and <PERSON><PERSON> Compose installed
- The tar files containing the Docker images

## Setup Instructions

1. **Load the Docker images**:
   ```bash
   # If you received a single compressed file
   tar -xzvf rag-docker-images.tar.gz
   
   # Load each image into Docker
   docker load -i rag-backend.tar
   docker load -i rag-frontend.tar
   docker load -i chroma.tar
   ```

2. **Create a .env file** with your API keys:
   ```bash
   echo "OPENAI_API_KEY=your_openai_api_key_here" > .env
   echo "GOOGLE_API_KEY=your_google_api_key_here" >> .env
   ```

3. **Create a docker-compose.yml file**:
   ```yaml
   version: '3.8'
   
   services:
     backend:
       image: yourusername/rag-backend:latest
       ports:
         - "5005:5005"
       volumes:
         - ./input_files:/app/input_files
       environment:
         OPENAI_API_KEY: ${OPENAI_API_KEY}
         GOOGLE_API_KEY: ${GOOGLE_API_KEY}
         CHROMA_DB_URL: http://chroma:8000
       depends_on:
         - chroma
       networks:
         - rag-network
   
     frontend:
       image: yourusername/rag-frontend:latest
       ports:
         - "3000:3000"
       environment:
         NEXT_PUBLIC_API_URL: http://localhost:5005
       depends_on:
         - backend
       networks:
         - rag-network
   
     chroma:
       image: chromadb/chroma
       ports:
         - "8000:8000"
       volumes:
         - chroma_data:/app/chroma_data
       networks:
         - rag-network
   
   volumes:
     chroma_data:
   
   networks:
     rag-network:
       driver: bridge
   ```

4. **Run the application**:
   ```bash
   docker-compose up
   ```

5. **Access the application**:
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:5005